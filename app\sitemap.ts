import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NODE_ENV === 'production'
    ? 'https://upzera.com'
    : 'http://localhost:3000'

  const routes = [
    '',
    '/about',
    '/contact',
    '/faq',
    '/testimonials',
    '/our-approach',
    '/website-development',
    '/chatbot-integration'
  ]

  const locales = ['en', 'lt']

  const sitemap: MetadataRoute.Sitemap = []

  // Add root URL (redirects to /en)
  sitemap.push({
    url: baseUrl,
    lastModified: new Date(),
    changeFrequency: 'daily',
    priority: 1.0,
    alternates: {
      languages: {
        en: `${baseUrl}/en`,
        lt: `${baseUrl}/lt`,
      }
    }
  })

  // Add all localized pages
  locales.forEach(locale => {
    routes.forEach(route => {
      sitemap.push({
        url: `${baseUrl}/${locale}${route}`,
        lastModified: new Date(),
        changeFrequency: route === '' ? 'daily' : 'weekly',
        priority: route === '' ? 1.0 : 0.8,
        alternates: {
          languages: {
            en: `${baseUrl}/en${route}`,
            lt: `${baseUrl}/lt${route}`,
          }
        }
      })
    })
  })

  return sitemap
}